import os
from subprocess import run, CalledProcessError, PIPE
import json
from datetime import datetime, timedelta
import mysql.connector
import time
import traceback
import threading

def cron_once():
    # 一次性的定时任务
    import cron_scan_weibo

    user_ids = ['lidangzzz']
    tasks = crawl_by_user_ids(user_ids)
    cron_scan_weibo.save2db(tasks, source=1)
    print(f'Cron job completed. {len(tasks)} tasks added to database.')



def cron_scan(start_now=False):
    # 定时任务
    # 每小时运行一次 cron_once()，当调用线程退出时自动退出

    def background_task():
        if not start_now:
            # Sleep for 1 hour (3600 seconds)
            time.sleep(3600)
        while True:
            try:
                print(f"[{datetime.now()}] Starting cron [SCAN_TWITTER] job...")
                cron_once()
                print(f"[{datetime.now()}] Cron job [SCAN_TWITTER] completed")
            except Exception as e:
                print(f"[{datetime.now()}] Error in cron job: {e}")
                traceback.print_exc()

            # Sleep for 1 day
            time.sleep(3600*24)

    # Create and start daemon thread
    # Daemon threads automatically exit when the main program exits
    cron_thread = threading.Thread(target=background_task, daemon=True)
    cron_thread.start()

    print("Cron background [SCAN_TWITTER] task started (runs every 1 day)")
    return cron_thread

    

def crawl_by_user_ids(user_ids):
    from twitterSelenium.crawl_by_user_id import crawl_multiple_users

    start_time = datetime.now() - timedelta(days=1)

    tweets = crawl_multiple_users(user_ids, start_time=start_time)
    
    tasks = []
    for user_id, user_tweets in tweets.items():
        for tweet in user_tweets:
            tasks.append({
                'tweet_id': tweet['tweet_id'],
                'created_at': int(tweet['create_time'].timestamp())
            })
    
    return tasks

    

if __name__ == '__main__':
    cron_once()